import { createFeatureSelector, createSelector } from '@ngrx/store';

import { UserState, userAdapter } from '../state/user.state';
import { IUser } from '../../model';

export const selectUserState = createFeatureSelector<UserState>('user');

export const getUserEntities = (state: UserState) => {
  // tslint:disable-next-line:no-unused-expression
  state.entities;
};

export const getUserLoading = (state: UserState) => state.loading;

export const getUserUploading = (state: UserState) => state.uploading;
export const getActionSuccess = (state: UserState) => state.success;
export const getOffset = (state: UserState) => state.offset;
export const getLimit = (state: UserState) => state.limit;
export const getDataLength = (state: UserState) => state.dataLength;
export const getSearch = (state: UserState) => state.search;
export const getFields = (state: UserState) => state.fields;
export const getCardName = (state: UserState) => state.cardName;
export const getUserInfo = (state: UserState) => state.userInfo[0];
export const getAddedUser = (state: UserState) => state.lastAddedUser;
export const getPasswordChaned = (state: UserState) => state.passwordChanged;
export const getUsersStructured = (state: UserState) => state.structuredUsers;
export const getRegisterSuccess = (state: UserState) => state.isRegisterSuccess;
export const getGroupedUsers = (state: UserState) => state.groupedUsers;
export const getUserFirstLoading = (state: UserState) => state.firstLoading;

const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal
} = userAdapter.getSelectors();

export const selectAllIDS = createSelector(
  selectUserState,
  selectIds
);

export const selectAllData = createSelector(
  selectUserState,
  selectAll
);

export const selectAllEntities = createSelector(
  selectUserState,
  selectEntities
);

export const selectOffset = createSelector(
  selectUserState,
  getOffset
);
export const selectSearch = createSelector(
  selectUserState,
  getSearch
);

export const selectFields = createSelector(
  selectUserState,
  getFields
);

export const selectCardName = createSelector(
  selectUserState,
  getCardName
);

export const selectDataLength = createSelector(
  selectUserState,
  getDataLength
);

export const selectLoading = createSelector(
  selectUserState,
  getUserLoading
);
export const selectUploading = createSelector(
  selectUserState,
  getUserUploading
);
export const selectActionSuccess = createSelector(
  selectUserState,
  getActionSuccess
);

export const selectLimit = createSelector(
  selectUserState,
  getLimit
);

export const selectUserInfo = createSelector(
  selectUserState,
  getUserInfo
);

export const selectAddedItem = createSelector(
  selectUserState,
  getAddedUser
);

export const selectPasswordChanged = createSelector(
  selectUserState,
  getPasswordChaned
);

export const selectUserById = (userId:number)=> createSelector(
  selectAllData,
  (users: IUser[]) => {
    return users.filter((order:IUser) => order.id == userId)[0]
  }
);

export const selectAllDataStructures = createSelector(
  selectUserState,
  getUsersStructured
);

export const selectRegisterSuccess = createSelector(
  selectUserState,
  getRegisterSuccess
);
export const selectGroupedUsers = createSelector(
  selectUserState,
  getGroupedUsers
);

export const selectFirstLoading = createSelector(
  selectUserState,
  getUserFirstLoading
);

