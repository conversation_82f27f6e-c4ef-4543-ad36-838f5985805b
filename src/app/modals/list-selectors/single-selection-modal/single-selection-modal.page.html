
<ion-header [translucent]="true">
  <ion-toolbar>
    <ion-title>{{'SELECT_YOUR_' | translate}}{{title}}</ion-title>
  </ion-toolbar>
  <ion-toolbar>
    <ion-searchbar debounce="1000" mode="ios" placeholder="{{'SEARCH' | translate}}" (ionInput)="filterItems($event)"></ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true">
  <ion-list>
    <ion-item *ngFor="let value of selectionItems">
      <ion-checkbox (ionChange)="updateSelectedValues(value)">{{value[1]}}</ion-checkbox>
    </ion-item>
  </ion-list>
</ion-content>
