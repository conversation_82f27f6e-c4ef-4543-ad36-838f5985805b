import { Component, Input, OnInit } from '@angular/core';
import { ModalController } from '@ionic/angular';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';

@Component({
  selector: 'app-single-selection-modal',
  templateUrl: './single-selection-modal.page.html',
  styleUrls: ['./single-selection-modal.page.scss'],
})
export class SingleSelectionModalPage implements OnInit {
  @Input() title!:string
  selectionItems!:any[]
  selectedValue: any;
  originSelectionItems!: any[] ;
  constructor(
    private modalCtrl:ModalController,
    private odooRpc:OdooJsonRPC
  ) { }

  ngOnInit() {
    this.originSelectionItems = this.selectionItems
  }
  filterItems(event : any){
    if(event.detail.value == ''){
      this.selectionItems = this.originSelectionItems 
    }
    else{
      this.selectionItems = this.selectionItems.filter(item => item[1].toLowerCase().includes(event.detail.value.toLowerCase()));
    }
  }
  updateSelectedValues(value : any){
    this.selectedValue = value
    this.modalCtrl.dismiss(this.selectedValue)
  }

}
