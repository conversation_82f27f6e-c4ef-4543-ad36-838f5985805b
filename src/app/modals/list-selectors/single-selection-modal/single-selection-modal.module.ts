import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { IonicModule } from '@ionic/angular';

import { SingleSelectionModalPageRoutingModule } from './single-selection-modal-routing.module';

import { SingleSelectionModalPage } from './single-selection-modal.page';
import { TranslateModule } from '@ngx-translate/core';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    SingleSelectionModalPageRoutingModule,
    TranslateModule,
  ],
  declarations: [SingleSelectionModalPage]
})
export class SingleSelectionModalPageModule {}
