<ion-header mode="ios" style="background: #FFFFFF;">

  <ion-toolbar>
    <ion-buttons slot="start">
      <ion-button color="dark" (click)="close()">
        <ion-icon name="chevron-back-outline" style="font-size: 1.5rem;"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title style="text-align: center;">
      &nbsp;<span *ngIf="order_sequence" style="color: #252745;" translate>{{order_sequence}}</span>
    </ion-title>
    <ion-buttons slot="end">
      <ion-button color="dark" (click)="doRefresh()">
        <ion-icon src="../assets/icon/refresh-icon.svg"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
  <div style="text-align: center;padding:5px;font-size: 15px;">
    &nbsp;<span *ngIf="order_sequence" style="color: #252745;" translate>{{second_tittle}}</span>
  </div>
  <div class="chat-baceground-top">

  </div>
</ion-header>


<ion-content #scrollElement [scrollEvents]="true" (ionScroll)="logScrolling($event)" onload="goToBottom()">
  
  <div *ngIf="!load">
    <div *ngFor="let msg of msgList; let last = last">
      <chat-item [msg]='msg' [user_id]='current_user_id' class="speech-bubble">
      </chat-item>
      
    </div>
  </div>
  
  <div *ngIf="load" class="empty-content-message">
    <ion-spinner name="crescent"></ion-spinner>
    <P>
      {{'PLEASE_WAIT' | translate}}
    </P>
  </div>

  <ion-infinite-scroll threshold="100px" (ionInfinite)="loadData($event)" position="top">
    <ion-infinite-scroll-content loadingSpinner="lines"></ion-infinite-scroll-content>

  </ion-infinite-scroll>
</ion-content>

  <ion-footer>
    <ion-toolbar>
      <div class="input-icon-wrap">
        <ion-input class="input-style" [(ngModel)]="editorMsg" 
          placeholder="{{'WRITE_YOUR_MESSAGE_HERE'| translate}}" >
        </ion-input >
        <div>
          <ion-button class="send-msg-btn" [disabled]="editorMsg == '' " #sendButton ion-button clear icon-only item-right
            (click)="sendMsg()">
            <ion-icon  [ngClass]="currentLang == 'en' ? 'send-icon-en' :'send-icon'" name="send-outline"></ion-icon>
          </ion-button> 
        </div>
      </div>
    </ion-toolbar>

    <ion-fab *ngIf="showArrow && !changeColorArrow" style="top:-80px !important" horizontal="end" slot="fixed">
      <ion-fab-button (click)="goToBottom()">
        <ion-icon name="arrow-down-outline"></ion-icon>
      </ion-fab-button>
    </ion-fab>

    <ion-fab *ngIf="showScrollUp" style="top:-80px !important" horizontal="start" slot="fixed">
      <ion-fab-button (click)="updateScrollForUp()" class="new-message" color="secondary">
        <ion-icon name="arrow-up-outline"></ion-icon>
      </ion-fab-button>
    </ion-fab>

  </ion-footer>
