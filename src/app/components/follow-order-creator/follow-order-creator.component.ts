import { Component, Input, OnInit } from '@angular/core';
import { ModalController, ToastController } from '@ionic/angular';
import { TranslateService } from '@ngx-translate/core';
import { OrderState } from 'src/app/ngrx-store/order/store';
import { IonicModule } from '@ionic/angular';
import { BarcodeScanComponent } from 'src/app/modals/barcode-scan/barcode-scan.component';
import { dialogService } from 'src/app/services/error-handlar-service';
import { from, of } from 'rxjs';
import { OdooJsonRPC } from 'src/app/services/odooJsonRPC';
import { map, catchError } from 'rxjs/operators';


@Component({
    selector: 'app-follow-order-creator',
    templateUrl: './follow-order-creator.component.html',
    styleUrls: ['./follow-order-creator.component.scss'],
})


export class FollowOrderCreatorComponent implements OnInit {

    dir: string = '';
    followOrdersSequences: string[] = []
    followOrdersNames: string[] = []
    editable: boolean[] = [];  
    isVisible: boolean = true;
    showAddFollowOrder: boolean = true;
    editedOrders: { [index: string]: number } = {};
    @Input() disableEdit:boolean=false

    constructor(
        private modalCtrl: ModalController,
        private toastCtrl: ToastController,
        private translate: TranslateService,
        private dialogService: dialogService,
        private odooRpc: OdooJsonRPC,

    ) { }


    ngOnInit(): void {
        console.log(this.editable)
        this.dir = document.documentElement.getAttribute('dir') || 'ltr';
    }


    close() {
        this.modalCtrl.dismiss()
    }



    onChangeValue(event: any, index: number) {
        const match = event.srcElement.name.match(/\d+/);
        if (match) {
            this.followOrdersNames[parseInt(match[0])] = event.srcElement.value;
        }
        if (this.editable[index]) {
            const sequence = this.followOrdersSequences[index];
            if (!(sequence in this.editedOrders)) {
                this.editedOrders[sequence] = index;
            }
        }
    }

    AddFollowOrders() {
        Object.entries(this.editedOrders).forEach(([sequence, index]) => {
            this.odooRpc.call('rb_delivery.follow_up_order', 'search', [[['follow_up_sequence', '=', sequence]]]).then(order => {
                console.log(order)
                if(order.body && order.body.result && order.body.result.result && order.body.result.result.length > 0) {
                    this.odooRpc.call('rb_delivery.follow_up_order', 'write', [[order.body.result.result[0]], {'name': this.followOrdersNames[index]}]).then(res => {
                        console.log(res)
                    });
                }
            });
        });
        this.modalCtrl.dismiss({
            followOrdersSequences: this.followOrdersSequences,
            followOrdersNames: this.followOrdersNames
        })
    }


    scanMore() {
        this.openScanner().then(scanResult => {
            if (scanResult.data && !this.followOrdersSequences.includes(scanResult.data.scannedValue)) {
                if (scanResult.data.scannedValue) {
                    this.followOrdersSequences.push(scanResult.data.scannedValue as string)
                    this.followOrdersNames.push('')
                    this.editable.push(false)
                }
            } else if (scanResult.data) {
                this.dialogService.warning({
                    input: this.translate.instant('BARCODE_ALREADY_SCANNED'),
                    message: this.translate.instant('BARCODE_ALREADY_SCANNED'),
                    whatToDo: this.translate.instant('PLEASE_SCAN_OTHER_BARCODE'),
                    code: '1300',
                })
            }
        })
    }

    openScanner(): Promise<any> {
        this.isVisible = false;
        return new Promise((resolve, reject) => {
            this.modalCtrl.create({
                component: BarcodeScanComponent,
                backdropDismiss: false,
                componentProps: {
                    typeSelectionItems: ['SEQUENCE']
                },
                cssClass: "exclude-hide"
            }).then(modal => {
                modal.present();
                modal.onDidDismiss().then(scanResult => {
                    this.isVisible = true;
                    resolve(scanResult);
                })
            })
        });
    }

    getValue(index: number) {
        return this.followOrdersNames[index] || '';
    }

    removeFollow(item: any, index: number) {
        if(!this.editable[index]) {
            this.followOrdersSequences.splice(index, 1);
            this.followOrdersNames.splice(index, 1);
            this.editable.splice(index, 1);
        } else {
            this.odooRpc.call('rb_delivery.follow_up_order','search',[[['follow_up_sequence','=',this.followOrdersSequences[index]]]]).then(order=>{
                console.log(order)
                if(order.body && order.body.result && order.body.result.result && order.body.result.result.length > 0) {
                    this.odooRpc.call('rb_delivery.follow_up_order','unlink',[order.body.result.result[0]]).then(res=>{
                        if(res.body && res.body.result && res.body.result.success) {
                            this.followOrdersSequences.splice(index, 1);
                            this.followOrdersNames.splice(index, 1);
                            this.editable.splice(index, 1);
                            this.dialogService.success({
                                input: this.translate.instant('FOLLOW_UP_ORDER_DELETED_SUCCESSFULLY'),
                                message: this.translate.instant('FOLLOW_UP_ORDER_DELETED_SUCCESSFULLY'),
                            });

                        }
                    })
                } else {
                    this.dialogService.warning({
                        input: this.translate.instant('ERROR_WHILE_DELETING_FOLLOW_UP_ORDER'),
                        message: this.translate.instant('ERROR_WHILE_DELETING_FOLLOW_UP_ORDER'),
                        whatToDo: this.translate.instant('PLEASE_WAIT_AND_TRY_AGAIN_OR_CONTACT_YOUR_ADMINSTRATOR'),
                        code: '1300',
                    });
                }
            })
        }
    }
}

